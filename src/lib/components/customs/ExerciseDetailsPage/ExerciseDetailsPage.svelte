<script lang="ts">
   import * as Select from "$lib/components/ui/select";
	import { Button } from "$lib/components/ui/button";
	import { showExerciseDetailsPage, exerciseDetails, showAddExerciseMenuItem, showMoveExerciseMenuItem, current_workout_exercise_id, current_workout_id } from "$lib/stores/generalStore";
	import { ThreeDotsVertical, Pencil, FlagFill, Copy, Link45deg, PlusCircleFill, Disc, XLg, ExclamationTriangleFill, BoxArrowInLeft, Clipboard2Plus, QuestionCircle, Trash, Flag, ArrowBarDown, Search, X, ChevronLeft } from "svelte-bootstrap-icons";
    import * as Drawer from "$lib/components/ui/drawer";
    import * as Dialog from "$lib/components/ui/dialog";
    import { Input } from "$lib/components/ui/input";
    import { LineInput } from "$lib/components/ui/line-input";
    import * as Alert from "$lib/components/ui/alert";
    import { toast } from "svelte-sonner";
    import { onMount, onDestroy } from 'svelte';
    import type { IProgram, IWorkout } from "$lib/typings";

    import VideoPlayer from '../VideoPlayer/VideoPlayer.svelte';
	import type { Writable } from "svelte/store";
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';

  import { defaultProgram, selectedWorkout, selectedProgram, allThePrograms, helpsUrl } from "$lib/stores/generalStore";
	import { get } from "svelte/store";
	import { goto } from "$app/navigation";
  import { deleteExerciseWorkout } from "$lib/utils/workouts_exercises.utils";
	import { extractVideoId } from "$lib/utils";
	import VimeoPlayer from "../VimeoPlayer/VimeoPlayer.svelte";

  import * as AlertDialog from "$lib/components/ui/alert-dialog";


    let programWorkouts: any[] = $state([]);

    const { tagStore, programsStore, workoutStore, isPremium, programs } = $props<{ tagStore: Writable<any>, programsStore: Writable<any>, workoutStore: Writable<any>, isPremium: boolean, programs: IProgram[] }>();

    let openDrawer = $state(false);
    let openDropdown = $state(false);
    let openChooseTagDialog = $state(false);
    let openEditTagDialog = $state(false);
    let openNewTagDialog = $state(false);
    let openDeleteAlertDialog = $state(false);
    let dialogOpen: boolean = $state(false);

    let selectedTag = $state(null);
    let selectedColor = $state('');
    let selectedTagName = $state('');
    let selectedTagId = $state(0);
    let onVimeoOpen = $state(false);

    let videoEl: any;


    const colors = [
        '#a8e6ff', '#77dd77', '#b19cd9', '#ff85a2', '#98fb98',
        '#40e0d0', '#fdfd96', '#d3d3d3', '#ffb347', '#779ecb'
    ];

    let isLoading = $state(false);
    let isUiLoading = $state(true);
    let tagUiList = $state([]);
    let exerciseTaggedId = $state([]);
    let exerciseId = $state(0);
    let isExerciseInTag = $derived.by(() => {
        if(selectedTag){
            return exerciseTaggedId.some((id) => selectedTag?.exercises?.includes(id));
        }
    });
    
    let isDesktop: boolean = $state(false);
    let isTablet: boolean = $state(false);
    let isBtwMdAndLg: boolean = $state(false);
    let isMobile: boolean = $state(false);
    let isLogoutLoading: boolean = $state(false);
    let openBottomDrawer: boolean = $state(false)

    let workouts: any[] = $state([]);

    
    let selectedProgramFromStore: { value: string; label: string } | null = $state(null);
    let selectedWorkoutFromStore: { value: string; label: string } | null = $state(null);
    let selectedExercise: {
        name: string;
        id: string;
        img: string;
        cat: string;
        icon: string;
        position: string;
        invisible: boolean;
        video_src: string;
    } | undefined = $state(undefined);
    let open = $state(false);
    let isVideoPlayerReady = $state(false);
    let openDeleteExerciseDialog: boolean = $state(false);

    onMount(() => {
        helpsUrl.set('https://exrx.net/WorkoutWebApp/ExerciseDetails');
        // fetchWorkouts(selectedProgram!.value);
        const getCurrentWorkout = get(selectedWorkout);
        if (getCurrentWorkout) {
          selectedWorkoutFromStore = getCurrentWorkout
        }
        // const workout = data.program.workouts[0];
    });

    $effect(() => {
        
        if ($selectedProgram && $selectedProgram.value) {
            selectedProgramFromStore = $selectedProgram;
            workouts = $programsStore.filter((program: IProgram) => program.id === $selectedProgram?.value)[0]?.workouts;

            
        }
    });

    // $effect(() => {        
    //     if ($current_workout_exercise_id) {
    //         fetchWorkouts($selectedProgram.value)
            
    //     }
    // });


    $effect(() => {
        if (workouts && workouts.length > 0) {
          console.log('selected', $selectedWorkout);
            // selectedWorkoutFromStore = $selectedWorkout?.value ? $selectedWorkout : { value: workouts[0]?.id, label: workouts[0]?.name || workouts[0]?.automated_name };

            // selectedWorkoutFromStore = $selectedWorkout;
        }
    });


    async function fetchWorkouts(programId: string) {
        try {
            const response = await fetch(`/api/workouts?program=${programId}`);
            const data = await response.json();

            workouts = data.data;
            
            if(!$selectedWorkout || $selectedWorkout.value === null || $selectedWorkout.value === undefined){
                selectedWorkout.set({value: data.data[0].id, label: data.data[0].name})
            }

            
            return data;
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    }


    function copyToClipboardFallback(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            document.execCommand('copy');
            toast.success('URL copied to clipboard');
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
        document.body.removeChild(textarea);
    }

    function copyToClipboard(text: string) {

    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text)
        .then(() => {
            toast.success('URL copied to clipboard');
        })
        .catch(err => {
            console.error('Failed to copy URL:', err);
        });
    } else {
        copyToClipboardFallback(text);
    }

    
    }
    const getExerciseId = async () => {
        const res = await fetch(`/api/tags?name=${$exerciseDetails.Exercise_Name_Complete_Abbreviation}`, {
        method: 'GET'
        });

        const data = await res.json();

        if(data.success && data.message === "Empty"){
        exerciseId = 0;
            isUiLoading = false;

        } else if (data.success && data.data){
            const temp = data.data[0].id;
            exerciseId = temp;
            getExerciseTags(exerciseId);
            isUiLoading = false;
        }
    }

    const getExerciseTags = async (id: number) => {
        const res = await fetch(`/api/tags?exerciseId=${id}`, {
        method: 'GET'
        });

        const data = await res.json();
        if(data.success && data.message === "Empty"){
        return;
        } else if (data.success && data.data){
data.data.forEach(tag => {
    if (tag.tags_id !== null) {
        exerciseTaggedId.push(tag.id);
        tagUiList.push(tag);
    }
});
        }
    }

    const handleCancelChooseTagFn = () => {
        openChooseTagDialog = false;
        selectedTag = null;
        selectedTagName = "";
    }

    const handleCancelOpenTagFn = () => {
        openNewTagDialog = false;
        selectedColor = "";
        selectedTagName = "";
    }

    const handleCancelEditTagFn = () => {
        openEditTagDialog = false;
        selectedTag = null;
        selectedTagName = "";
        selectedColor = "";
    }

    const handleEditDeleteFn = () => {
        openDeleteAlertDialog = !openDeleteAlertDialog;
        openEditTagDialog = false;
    }

    const handleNewTagSave = async () => {
        isLoading = true;
        const res = await fetch('/api/tags', {
        method: 'POST',
        body: JSON.stringify({name: selectedTagName, color: selectedColor })
        });

        const data = await res.json();

        if(data.success) {
            tagStore.set([... $tagStore, data.data]);
            toast.success("Tag created successfully!");

            openNewTagDialog = false;
            openChooseTagDialog = true;
            selectedColor = "";
            selectedTagName = "";
            isLoading = false;
            tagUiList = [];
            getExerciseId();
        }
    }
    const handleChooseTagSave = async () => {
    isLoading = true;

    const res = await fetch('/api/tags', {
      method: 'POST',
      body: JSON.stringify({tagId: selectedTagId, exerciseId: exerciseId})
    })
    
    const data = await res.json();

    if(data.success) {
      const tempArr = $tagStore;
      const tagToUpdate = tempArr.find(tag => tag.id === data.data?.tags_id);
      
      if(tagToUpdate){
        const updatedTag = {...tagToUpdate};
        updatedTag.exercises.push(data.data?.id);

        const updatedTags = tempArr.filter(tag => tag.id !== data.data?.tags_id);
        updatedTags.push(updatedTag);
        tagStore.set(updatedTags);
      }

      toast.success("Exercise tagged successfully")
      selectedTag = null;

      openChooseTagDialog = false;
      selectedTagName = "";
      selectedTagId = 0;
      isLoading = false;
      tagUiList = [];
      getExerciseId();
    } else {
      toast.warning(data.message);
      selectedTag = null;
      openChooseTagDialog = false;
      openNewTagDialog = false;
      selectedTagName = "";
      selectedTagId = 0;
      isLoading = false;
    }
  }

  const handleEditTagSave = async () => {
    isLoading = true;
    const res = await fetch('/api/tags', {
      method: 'PATCH',
      body: JSON.stringify({tagId: selectedTagId, name: selectedTagName, color: selectedColor})
    })

    const data = await res.json();
    if(data.success){
      const temp = $tagStore.filter((tag) => tag.id !== data.data.id);
      tagStore.set([...temp, data.data]);
      toast.success("Tag edited successfully");
      selectedTag = null;
      openEditTagDialog = false;
      selectedTagName = "";
      selectedTagId = 0;
      selectedColor = "";
      isLoading = false;
      tagUiList = [];
      getExerciseId();
    } else {
      toast.warning(data.message);
      selectedTag = null;
      openEditTagDialog = false;
      selectedTagName = "";
      selectedTagId = 0;
      selectedColor = "";
      isLoading = false;
      tagUiList = [];
      getExerciseId();
    }
  }

  const handleXClick = () => {
      selectedTag = null;
        selectedTagName = "";
        selectedColor = "";
  }

  const handleDeleteAlertCancel =  () => {
    openDeleteAlertDialog = !openDeleteAlertDialog;
    selectedTag = null;
    selectedColor = "";
    selectedTagName = "";
  }

  const handleDeleteAlertAll = async () => {
    isLoading = true;
    const res = await fetch(`/api/tags?tagId=${selectedTagId}`, {
      method: 'DELETE',
    })
    
    const data = await res.json();

    if(data.success){
      const temp = $tagStore.filter((tag) => tag.id != selectedTagId);
      tagStore.set(temp);
      toast.success("Tag deleted successfully");
      openDeleteAlertDialog = false;
      selectedTag = null;
      selectedTagId = 0;
      selectedColor = "";
      selectedTagName = "";
      isLoading = false;
      tagUiList = [];
      getExerciseId();
    }
  }

  const handleDeleteAlertSingle = async () => {
    isLoading = true;
    const res = await fetch(`/api/tags?tagId=${selectedTagId}&exerciseId=${exerciseId}`, {
      method: 'DELETE',
    });

    const data = await res.json();

    if(data.success){
      
      toast.success("Exercise untagged successfully");
      openDeleteAlertDialog = false;
      selectedTag = null;
      selectedTagId = 0;
      selectedColor = "";
      selectedTagName = "";
      isLoading = false;
      tagUiList = [];
      getExerciseId();
    }
  }

  let filteredTags = $derived(
  $tagStore.filter(tag => 
    tag.name.toLowerCase().includes(selectedTagName.toLowerCase())
  )
);

function handleTagSelect(tag) {
  selectedTag = tag;
  selectedTagName = tag.name;
  selectedTagId = tag.id;
}

  $effect(() => {
    if (selectedTag) {
      if(openEditTagDialog){
        selectedColor = selectedTag.color;
      }
    }
  });

  // Auto-select tag if there's exactly one tag in tagUiList
  $effect(() => {
    if (tagUiList.length === 1) {
      const tag = tagUiList[0];
      selectedTag = tag.tags_id;
      selectedTagName = tag.tags_id.name;
      selectedTagId = tag.tags_id.id;
    }
  });

  onMount(() => {
    if($exerciseDetails){      
      getExerciseId();
    } else {
      isUiLoading = false;
    }
        

    
    const checkScreenSize = () => {
      isDesktop = window.innerWidth >= 1033;
      isMobile = window.innerWidth < 768;	  
	    isBtwMdAndLg = window.innerWidth >= 768 && window.innerWidth <= 1033; // md breakpoint
    };

    checkScreenSize();
    window.addEventListener('resize', () => {
      checkScreenSize();
    });
    
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  });

  $effect(() => {
        if (selectedExercise && selectedExercise.video_src) {
            isVideoPlayerReady = false; // Reset when video source changes
        } else if (!selectedExercise) {
            isVideoPlayerReady = true; // No exercise, so no video to wait for
        }
    });

  function checkScreenSize () {
    if (window.innerWidth >= 1033) {
      isDesktop = true;
    } 
    else if (window.innerWidth < 1033 && window.innerWidth >= 768) {
      isTablet = true;
    } 
    else {
      isDesktop = false;
      isTablet = false;
      isMobile = true;
    }
  };

    function openMenu() {
      const checkCurrentSize = checkScreenSize();
      if (isDesktop || isTablet) {
        console.log('desktop or tablet');
        openDropdown = !openDropdown;
      } else {
      openDrawer = !openDrawer;
    }
  }
  async function handleLogout() {
        isLogoutLoading = true;
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'POST'
            });

            if (response.ok) {
                isLogoutLoading = false;
                toast.success('Successfully logged out');
                goto('/login');
            } else {
                toast.error('Failed to logout');
            }
        } catch (error) {
            console.error('Logout error:', error);
            toast.error('Failed to logout');
        } finally {
            isLogoutLoading = false;
        }
    }

    const sendToHelpSection = () => {
	  window.open('https://exrx.net/WorkoutWebApp/ExerciseDetails', '_blank');
    }
  
  onDestroy(() => {
    if ($showExerciseDetailsPage) {
      showExerciseDetailsPage.set(false);
      exerciseDetails.set(null);
    }
    showAddExerciseMenuItem.set(false);
    showMoveExerciseMenuItem.set(false);
  });

  async function handleAddExercise() {
    dialogOpen = false;
    const body = {
        exercise: {
            id: $exerciseDetails.Exercise_Id, 
            exercise_id: $exerciseDetails?.Exercise_Id,
            name: $exerciseDetails?.Exercise_Name,
            cat: $exerciseDetails?.Overall_Category,
            img: $exerciseDetails?.Small_Img_1,
            icon: $exerciseDetails?.Utility_Icon,
            invisible: $exerciseDetails?.invisible,
            url: $exerciseDetails?.URL,
            video_src: $exerciseDetails?.video_src,
            Larg_Img_1: $exerciseDetails?.Larg_Img_1,
        },
        position: 0,
        workout_id: get(selectedWorkout)?.value
    };
    const response = await fetch('/api/workouts_exercises', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({items: [body]})
    });

    const data = await response.json();
    $exerciseDetails.workout_exercise_id = data.id;  

    workoutStore.update((current_workouts: IWorkout[]) => {
      return current_workouts.map(workout => {
        if (workout.id !== data.workout_id) return workout;

      // return {...workout, exercises: []};

        
        workout.exercises.push({
          id: data.id,
          data_id: data.id,
          exercise_id: $exerciseDetails?.Exercise_Id,
          name: $exerciseDetails?.Exercise_Name,
          cat: $exerciseDetails?.Overall_Category,
          img: $exerciseDetails?.Small_Img_1,
          icon: $exerciseDetails?.Utility_Icon,
          invisible: $exerciseDetails?.invisible,
          url: $exerciseDetails?.URL,
          video_src: $exerciseDetails?.video_src,
          Larg_Img_1: $exerciseDetails?.Larg_Img_1,
        });
        return workout;
      });
    });

    if (response.ok) {
        toast.success('Exercise added successfully'); 
        if (!get(current_workout_exercise_id)) return;
        const handleDeleteExerciseRes = await handleDeleteExercise(get(current_workout_exercise_id));

            if (handleDeleteExerciseRes) {
                toast.success('Exercise deleted successfully');
            // set current workout id to the response id
              current_workout_exercise_id.set(data.id);

            } else {
                toast.error(data.message || 'Failed to delete exercise from source workout');
            }

      }
          return;
    
}


async function handleDeleteExercise(workout_id: string | number | null) {
        const theWorkout_id =  Number(workout_id)

        const res = await deleteExerciseWorkout(theWorkout_id);
        // return res;
        if (!res) {
            toast.error("An error occurred while deleting the exercise from the workout");
            return false;
        }

        workoutStore.update((current_workouts: IWorkout[]) => {
          return current_workouts.map(workout => {
            if (workout.id !== get(current_workout_id)) return workout;
           
            const newExercises = workout.exercises.filter(ex => ex.id !== theWorkout_id);
            return { ...workout, exercises: newExercises };
          });
        });
        
        return true

    }
</script>

{#if isUiLoading}
    <div class="h-full w-full flex items-center justify-center">
        <Disc class="animate-spin h-10 w-10 text-[#FC570C]" />
    </div>
{:else}
    {#if $exerciseDetails}

    <div class="h-[calc(100vh-100px)] sm:h-full overflow-y-auto bg-[#F0F2FF]">
      <div class="h-full">
        <!-- Top Navigation -->
        <div class="flex justify-between items-center mt-3 mb-2 pb-0 px-4">
          <Button variant="ghost" class="text-black p-0" on:click={() => {showExerciseDetailsPage.set(false)}}>
            <XLg class="size-5 xs:size-6" />
          </Button>

          <!-- {#if isPremium && $exerciseDetails?.invisible == "1"} -->
           <div>
           {#if isMobile}
            <Button variant="ghost" class="p-0" onclick = {() => {openMenu()}}>
            <ThreeDotsVertical class="size-5 xs:size-6" />
            </Button>
            {:else}
              <!-- // dropdown from top for desktop and tablet screens -->
         <div>
          <DropdownMenu.Root>
            {#if !isMobile}
            <DropdownMenu.Trigger>
              <ThreeDotsVertical class="hidden md:block size-6 text-primary" />
            </DropdownMenu.Trigger>
            {/if}
            <DropdownMenu.Content    
              class="flex flex-col gap-2 p-4 max-w-[600px] mx-auto sm:top-0">
              
          {#if $showAddExerciseMenuItem}
            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
              on:click={() => {
                  fetchWorkouts($selectedProgram.value);
                  dialogOpen = !dialogOpen;
                }}                   
            >
            <Clipboard2Plus class="size-6" />
            <span class="flex-1">Add Exercise</span>
            </DropdownMenu.Item >
           {/if}
           {#if $showMoveExerciseMenuItem}
              <DropdownMenu.Item 
                class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
                on:click={() => {
                  fetchWorkouts($selectedProgram.value);
                  dialogOpen = !dialogOpen;
                }}
                >
                <ArrowBarDown class="size-6" />
              <span class="flex-1">Move Exercise</span>
            </DropdownMenu.Item >
              <DropdownMenu.Item 
                class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
                on:click={() => {
                  openDeleteExerciseDialog = true;               
                }}
                >
                <Trash class="size-6" />
                <span class="flex-1">Delete Exercise</span>
              </DropdownMenu.Item >
            {/if}

            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"on:click={ () => { 
            openDrawer = false; 
            openChooseTagDialog = true;
            } }>
            <Flag class="size-6" />
            <span class="flex-1">Choose Tag</span>
            </DropdownMenu.Item >

            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={ () => { 
                openDrawer = false; 
                openEditTagDialog = true;
              } }>
              <Pencil class="size-6" />
            <span class="flex-1">Edit Tag</span>
            </DropdownMenu.Item >

            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={ () => { 
                copyToClipboard(`${$exerciseDetails.Exercise_Name_Complete_Abbreviation}\n${$exerciseDetails.URL}`);
              } }>
              <Copy class="size-6" />
            <span class="flex-1">Copy Exercise Name & URL</span>
            </DropdownMenu.Item >

            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={ () => {
                copyToClipboard($exerciseDetails.Exercise_Name_Complete_Abbreviation);
              } }>
              <Copy class="size-6" />
            <span class="flex-1">Copy Exercise Name</span>
            </DropdownMenu.Item >

            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]" on:click={ () => { 
                copyToClipboard($exerciseDetails.URL);
              } }>
              <Copy class="size-6" />
            <span class="flex-1">Copy Exercise URL</span>
            </DropdownMenu.Item >
            <DropdownMenu.Item 
            class="flex gap-4 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
            on:click={() => {
              sendToHelpSection();
            }}
            >
            <QuestionCircle class="size-6" />
             <span class="flex-1">Help</span>
            </DropdownMenu.Item >
            
            <DropdownMenu.Item 
            class="flex gap-2 bg-[#F0F2FF] rounded justify-start items-center font-semibold text-[16px] text-[#606166]"
            on:click={() => {
              handleLogout();
              if (!isLogoutLoading) {
              openBottomDrawer = false;
              }
            }}
            disabled={isLogoutLoading}
            >
            {#if isLogoutLoading}
              <Disc class="mr-2 size-6 animate-spin" />
            {:else}
              <BoxArrowInLeft class="size-6" />
            {/if}
            <span class="flex-1">Logout</span>
            </DropdownMenu.Item >
            </DropdownMenu.Content>	
          </DropdownMenu.Root>
        </div>
        {/if}
            </div>
        </div>
  
      
            <!-- </Button> -->
          <!-- {/if} -->
        <!-- Main Content -->
        <div class="px-[4px] md:px-4 pb-4">
          <!-- Exercise Title -->
          <h1 class="text-xl lg:text-2xl font-semibold my-6 lg:mt-2 lg:mb-4 px-[4px]">
            {$exerciseDetails.Exercise_Name_Complete_Abbreviation}
          </h1>
          <!-- Two Column Layout -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            <!-- Left Column -->
            <div class="px-1 lg:bg-white lg:shadow-md lg:pt-2">
              <div class="w-full aspect-[4/3]">
                {#if !isPremium && $exerciseDetails?.invisible == "1"}
                <div class="w-full aspect-[4/3] flex flex-col border border-red-500 rounded-lg bg-white p-6">
                  <!-- Alert Header with Icon at the top -->
                  <div class="flex items-start gap-3 text-red-500">
                    <svg 
                      class="w-6 h-6 flex-shrink-0 mt-1" 
                      viewBox="0 0 16 16" 
                      fill="currentColor">
                      <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                    </svg>
                    <p class="text-red-500 font-medium">
                      You are not an ExRx.net Premium Subscriber. Please upgrade to Premium to view all information for premium exercises.
                    </p>
                  </div>

                  <!-- Button centered in remaining space -->
                  <div class="flex-1 flex items-center justify-center">
                    <Button on:click={() => window.open('https://exrx.net/Premium', '_blank', 'noopener,noreferrer')}
                      class="bg-[#FC570C] hover:bg-[#e54e0b] text-white font-semibold px-6 py-3 rounded-md transition-colors z-10"
                      style="pointer-events: auto;">
                      Upgrade to Premium
                    </Button>
                  </div>
                </div>
              {:else}
                {#if $exerciseDetails.video_src}
                 <VideoPlayer 
                  poster={$exerciseDetails.Larg_Img_1 ?? ""}
                  src={$exerciseDetails.video_src} 
                  onclick={() => onVimeoOpen = true} 
                  />
                {:else}
                  <button
                     onclick={() => onVimeoOpen = true}  
                  >
                    <img  
                    src={$exerciseDetails.Larg_Img_1 ?? ""} 
                    alt="video_not_available" class="w-full h-full" 
                  />
                  </button>
                {/if}
                {/if}
              </div>
              
              <div class="py-4">
                <div class="flex items-center gap-2 mb-4">
                    <img src="/diamond.svg" alt="diamond_svg" class="size-5 lg:size-6" class:hidden={$exerciseDetails?.invisible != "1"} />
                    <h2 class="text-xl font-medium italic text-nowrap xs:truncate" class:pl-9={$exerciseDetails?.invisible != "1" && !isMobile} class:pl-8={$exerciseDetails?.invisible != "1" && isMobile} class:pl-1={$exerciseDetails?.invisible == "1"}>
                      {$exerciseDetails.Overall_Category ?? "Default Overall Category"}
                    </h2>

                </div>
    
                <div class="flex items-center justify-between mb-4" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  <div class="flex items-center gap-2">
                    <img src={$exerciseDetails.Utility_Icon ?? "/GreyCatIcon.png"} alt="Utility_Icon" class="size-5 lg:size-6" />
                    <span class="text-xl font-medium italic pl-[2px] md:pl-0">
                      {$exerciseDetails.Utility_Name ?? "Default Utility Name"}
                    </span>
                  </div>
                  <span class="text-xl font-medium">
                    {$exerciseDetails.Movement_Name ?? "Default Movement Name"}
                  </span>
                </div>

                 {#if $exerciseDetails.Mechanics !== "" || $exerciseDetails.Force !== "" || ($exerciseDetails.Difficulty_Low !== 0 && $exerciseDetails.Difficulty_High !== 0)}
                  <div class="flex items-center justify-between mb-4" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  <span class="text-xl font-medium italic pl-7">
                    {$exerciseDetails.Mechanics ?? ""}  
                  </span>
                  <span class="text-xl font-medium italic">
                      {$exerciseDetails.Difficulty_Low ?? ""}
                      <span> - </span>
                      {$exerciseDetails.Difficulty_High ?? ""}
                    </span>
                  <span class="text-xl font-medium italic">
                    {$exerciseDetails.Force ?? ""}
                  </span>
                </div>
                 {/if}
    
                <!-- Tag Buttons -->
                <div class="flex flex-wrap gap-2" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  {#if tagUiList}
                    {#each tagUiList as tag}
                      <span class="inline-flex select-none items-center border px-3 py-1 text-[14px] text-[#423D3D] font-medium h-[29px] rounded-[20px]" style="background-color: {tag.tags_id.color}">
                          {tag.tags_id.name}
                      </span>
                    {/each}
                  {/if}
                </div>
              </div>
            </div>
    
            <!-- Right Column -->
            <div class="space-y-4 max-md:space-y-2 max-lg:space-y-3 p-1 lg:p-0">
              <h2 class="text-xl lg:text-2xl text-center lg:text-start font-semibold mb-4 lg:mb-2">Instructions</h2>
              
              <div class="mb-4 max-md:mb-2 max-lg:mb-3">
                <h3 class="text-xl font-semibold mb-2 max-md:mb-1 max-lg:mb-1">Preparation:</h3>
                <p class="text-base lg:text-lg text-[#555A62] font-medium leading-relaxed" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  {$exerciseDetails.Instructions_Preparation ?? "Default Preparation Instructions"}
                </p>
              </div>
    
              <div class="mb-4 max-md:mb-2 max-lg:mb-3">
                <h3 class="text-xl font-semibold mb-2 max-md:mb-1 max-lg:mb-1">Execution:</h3>
                <p class="text-base lg:text-lg text-[#555A62] font-medium leading-relaxed" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  {$exerciseDetails.Instructions_Execution ?? "Default Execution Instructions"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> 
    {:else}
    <!-- this is an instance where the exercise details doesn't return anything -->
    <div class="h-[calc(100vh-100px)] sm:h-full overflow-y-auto bg-[#F0F2FF]">
      <div class="h-full">
        <!-- Top Navigation -->
        <div class="flex justify-between items-center mb-2 pt-2 pb-0 pr-0 pl-4 sm:p-4">
          <Button variant="ghost" class="text-black p-0" on:click={() => {showExerciseDetailsPage.set(false)}}>
            <XLg class="size-6 sm:size-5" />
          </Button>
        </div>
        <!-- Main Content -->
        <div class="px-4 pb-4 no-select-no-copy">
          <!-- Exercise Title -->
          <h1 class="text-2xl lg:text-[30px] font-semibold mb-4 lg:mb-6" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
            {$exerciseDetails.Exercise_Name_Complete_Abbreviation ?? "Default Exercise Name"}
          </h1>
    
          <!-- Two Column Layout -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            <!-- Left Column -->
            <div class="p-1">
              <div class="w-full aspect-[4/3]">
                  {#if !isPremium && $exerciseDetails?.invisible == "1"}
                  <div class="w-full aspect-[4/3] flex flex-col border border-red-500 rounded-lg bg-white p-6">
                    <!-- Alert Header with Icon at the top -->
                    <div class="flex items-start gap-3 text-red-500">
                      <svg 
                        class="w-6 h-6 flex-shrink-0 mt-1" 
                        viewBox="0 0 16 16" 
                        fill="currentColor">
                        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/>
                      </svg>
                      <p class="text-red-500 font-medium">
                        You are not an ExRx.net Premium Subscriber. Please upgrade to Premium to view all information for premium exercises.
                      </p>
                    </div>

                    <!-- Button centered in remaining space -->
                    <div class="flex-1 flex items-center justify-center">
                      <Button class="bg-[#FC570C] hover:bg-[#e54e0b] text-white font-semibold px-6 py-3 rounded-md transition-colors z-10"
                      style="pointer-events: auto;">
                        Upgrade to Premium
                      </Button>
                    </div>
                  </div>
                {:else}
                  {#if $exerciseDetails.video_src}
                    <VideoPlayer 
                      poster={$exerciseDetails.Larg_Img_1 ?? ""}
                      src={$exerciseDetails.video_src} 
                      onclick={() => onVimeoOpen = true} 
                    />
                  {:else}
                    <button
                      onclick={() => onVimeoOpen = true}  
                    >
                      <img  
                      src={$exerciseDetails.Larg_Img_1 ?? ""} 
                      alt="video_not_available" class="w-full h-full" 
                    />
                    </button>
                  {/if}
                {/if}
              </div>
              
              <div class="px-1 py-4">
                <h2 class="text-xl lg:text-2xl font-semibold italic mb-3">
                  {$exerciseDetails.Overall_Category ?? "Default Overall Category"}
                </h2>
    
                <div class="flex items-center justify-between mb-4" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  <div class="flex items-center gap-2">
                    <div class="w-5 lg:w-6 h-5 lg:h-6 border border-black rounded-full" />
                    <span class="text-base lg:text-lg font-semibold italic">
                      {$exerciseDetails.Utility_Name ?? "Default Utility Name"}
                    </span>
                  </div>
                  <span class="text-base lg:text-lg font-semibold">
                    {$exerciseDetails.Movement_Name ?? "Default Movement Name"}
                  </span>
                </div>
    
                <!-- Tag Buttons -->
                <div class="flex flex-wrap gap-2" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  {#if tagUiList}
                    {#each tagUiList as tag}
                      <span class="inline-flex select-none items-center border px-2.5 py-0.5 text-xs text-[#423D3D] font-semibold h-[29px] rounded-[20px]" style="background-color: {tag.tags_id.color}">
                          {tag.tags_id.name}
                      </span>
                    {/each}
                  {/if}
                </div>
              </div>
            </div>
    
            <!-- Right Column -->
            <div class="space-y-4 max-md:space-y-2 max-lg:space-y-3">
              <h2 class="text-3xl font-semibold mb-4">Instructions</h2>
              
              <div class="mb-4 max-md:mb-2 max-lg:mb-3">
                <h3 class="text-xl font-semibold mb-2 max-md:mb-1 max-lg:mb-1">Preparation:</h3>
                <p class="text-base lg:text-lg text-[#555A62] font-medium leading-relaxed" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  {$exerciseDetails.Instructions_Preparation ?? "Default Preparation Instructions"}
                </p>
              </div>
    
              <div class="mb-4 max-md:mb-2 max-lg:mb-3">
                <h3 class="text-xl font-semibold mb-2 max-md:mb-1 max-lg:mb-1">Execution:</h3>
                <p class="text-base lg:text-lg text-[#555A62] font-medium leading-relaxed" class:blur-md={!isPremium && $exerciseDetails?.invisible == "1"}>
                  {$exerciseDetails.Instructions_Execution ?? "Default Execution Instructions"}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> 
    {/if}
    {#if isMobile}

    <Drawer.Root open={openDrawer} onOpenChange={(val: boolean) => { openDrawer = val }}>
    <Drawer.Content class="flex flex-col gap-2 p-4 max-w-[600px] w-full mx-auto sm:top-0 sm:bottom-[100px]">
      <Button variant="ghost" target="_blank"  href={$exerciseDetails.URL} class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
          openDrawer = false;

        } }>
        <Link45deg class="size-5" />
        Open URL In New Tab
      </Button>
<!--        <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166]" on:click={ () => {-->
<!--            openDrawer = false; -->
<!--            openEditTagDialog = true;-->
<!--          } }>-->
<!--          <Pencil class="size-5" />-->
<!--          Edit Tag-->
<!--        </Button>-->
        {#if $showAddExerciseMenuItem}
          <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
            openDrawer = false; 
            dialogOpen = true;
          } }>
            <ArrowBarDown class="size-5" />
            Add Exercise
          </Button>
        {/if}
        {#if $showMoveExerciseMenuItem}
          <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
            openDrawer = false; 
            dialogOpen = true;
          } }>
             <ArrowBarDown class="size-5" />
            <span class="">Move Exercise</span>
          </Button>
        {/if}
      <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
          openDrawer = false;
          openChooseTagDialog = true;
        } }>
        <Flag class="size-5" />
        Choose Tag
      </Button>
        <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold"
        on:click={() => {
        openDeleteExerciseDialog = true;
        openDrawer = false;              
        }}
          >
          <Trash class="size-5" />
          Delete Exercise
        </Button>
          <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
            openDrawer = false; 
            copyToClipboard(`${$exerciseDetails.Exercise_Name_Complete_Abbreviation}\n${$exerciseDetails.URL}`)
          } }>
          <Copy class="size-5" />
          Copy Exercise Name & URL
        </Button>
          <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
            openDrawer = false; 
            copyToClipboard(`${$exerciseDetails.Exercise_Name_Complete_Abbreviation}`)
          } }>
          <Copy class="size-5" />
          Copy Exercise Name
        </Button>
          <Button variant="ghost" class="flex gap-[13px] bg-[#F0F2FF] rounded justify-start items-center text-[16px] text-[#606166] font-semibold" on:click={ () => {
            copyToClipboard(`${$exerciseDetails.URL}`)
            openDrawer = false; 
          } }>
          <Copy class="size-5" />
          Copy Exercise URL
        </Button>
    </Drawer.Content>
    </Drawer.Root>
    {/if}
    <Dialog.Root open={openChooseTagDialog} onOpenChange={(val: boolean) => { openChooseTagDialog = val}}>
        <Dialog.Content class="sm:max-w-[412px] sm:max-h-[600px]">
          <Dialog.Header>
            <Dialog.Title>Choose Tag</Dialog.Title>
          </Dialog.Header>
          <div class="grid gap-4 py-4">
            <div class="flex flex-row gap-2">
                <div class="relative w-full">
                  <Search class="absolute left-2 top-1/2 -translate-y-1/2 text-black size-5" />
                  <Input type="text" name="tagName" bind:value={selectedTagName} placeholder="Find tags by name..." class="pl-10 w-full h-[45px] text-[16px] font-semibold placeholder:text-[#606166]" />
                  <X class="absolute right-2 top-1/2 -translate-y-1/2 text-[#999999] size-8 cursor-pointer" onclick={handleXClick} />
                </div>
            </div>
            <div class="flex items-start gap-2 py-[20px] overflow-y-auto">
              {#each filteredTags as tag}
              <button
                class="inline-flex select-none items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#FC570C] rounded-[20px] text-[#423D3D] h-[29px]"
                style="background-color: {tag.color}"
                onclick={() => handleTagSelect(tag)}
              >
                <span>{tag.name}</span>
              </button>
            {/each}
            </div>
            <div class="flex items-start">
              <Button variant="ghost" on:click={() => {openNewTagDialog = !openNewTagDialog; openChooseTagDialog = false}} class="px-0 text-[16px] text-[#606166] focus:bg-transparent hover:bg-transparent active:bg-transparent">
                <PlusCircleFill class="mr-2 size-7 text-[#FC570C]" />
                New Tag
              </Button>
            </div>
          </div>
          <Dialog.Footer>
            <div class="flex flex-row items-center justify-between gap-8">
              <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={() => {openEditTagDialog = !openEditTagDialog; openChooseTagDialog = false}} disabled={isLoading}>Edit</Button>
              <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleCancelChooseTagFn} disabled={isLoading}>Cancel</Button>
              <Button variant="ghost" class="flex gap-2 {isLoading ? 'text-[#313da3]' : 'text-[#FC570C]'} rounded justify-start items-center text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" disabled={!selectedTag || isLoading} onclick={handleChooseTagSave}>
                {#if isLoading}
                  Save 
                  <Disc class="size-6 animate-spin" />
                {:else}
                  Save
                {/if}
              </Button>
            </div>
          </Dialog.Footer>
        </Dialog.Content>
    </Dialog.Root>
    <Dialog.Root open={openNewTagDialog} onOpenChange={(val: boolean) => { openNewTagDialog = val}}>
            <Dialog.Content class="sm:max-w-[412px] sm:max-h-[600px]">
              <ChevronLeft class="size-5 top-4 cursor-pointer" onclick={() => {openNewTagDialog = !openNewTagDialog; openChooseTagDialog = true}} />
              <Dialog.Header>
                <Dialog.Title>New Tag</Dialog.Title>
              </Dialog.Header>
              <div class="grid gap-4 py-6">
                <div class="flex flex-row gap-2">
                  <div class="relative w-full">
                    <LineInput type="text" name="tagName" bind:value={selectedTagName} placeholder="Tag Name" class="w-full h-[45px] text-[16px] font-semibold focus:outline-none placeholder:text-[#606166]"/>
                    <X class="absolute right-2 top-1/2 -translate-y-1/2 text-[#999999] size-8 cursor-pointer" onclick={handleXClick} />
                  </div>
                </div>
                <div class="max-w-sm bg-white">
                  <div class="my-6">
                    <p class="text-[20px] text-[#606166] mb-[25px] font-semibold">Choose Color</p>
                    <div class="grid grid-cols-5 gap-2">
                      {#each colors as color}
                        <button 
                          class="size-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500 transition-transform transform hover:scale-110"
                          style:background-color = {color}
                          class:ring-2={selectedColor === color}
                          onclick={() => selectedColor = color}
                        >
                          {#if selectedColor === color}
                            <span class="text-[#606166] text-shadow">✓</span>
                          {/if}
                        </button>
                      {/each}
                    </div>
                  </div>
              </div>
              <Dialog.Footer>
                <div class="flex flex-row justify-center items-center gap-10">
                  <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleCancelOpenTagFn} disabled={isLoading}>Cancel</Button>
                  <Button variant="ghost" class="flex gap-2 {isLoading ? 'text-[#313da3]' : 'text-[#FC570C]'} rounded justify-start text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" disabled={!selectedColor || !selectedTagName || isLoading} onclick={handleNewTagSave}>
                    {#if isLoading}
                      Create 
                      <Disc class="size-6 animate-spin" />
                    {:else}
                      Create
                    {/if}
                  </Button>
                </div>
              </Dialog.Footer>
            </Dialog.Content>
    </Dialog.Root>

    <Dialog.Root open={openEditTagDialog} onOpenChange={(val: boolean) => { openEditTagDialog = val}}>
        <Dialog.Content class="sm:max-w-[412px] sm:max-h-[600px]">
          <Dialog.Header>
            <Dialog.Title>Edit Tag</Dialog.Title>
          </Dialog.Header>
          <div class="grid gap-4 py-6">
            <div class="flex flex-row gap-2">
                  <div class="relative w-full">
                    <Search class="absolute left-2 top-1/2 -translate-y-1/2 text-black size-5" />
                    <Input type="text" name="tagName" bind:value={selectedTagName} placeholder="Tag Name" class="pl-10 w-full h-[45px] text-[16px] font-semibold focus:outline-none placeholder:text-[#606166]"/>
                    <X class="absolute right-2 top-1/2 -translate-y-1/2 text-[#999999] size-8 cursor-pointer" onclick={handleXClick} />
                  </div>
            </div>
            <div class="flex items-start gap-2 pt-[20px] overflow-y-auto">
              {#each filteredTags.filter(tag => (!selectedTag && selectedColor) ? tag.color === selectedColor : true) as tag}
              <button
                class="inline-flex select-none items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#FC570C] rounded-[20px] text-[#423D3D] h-[29px]"
                style="background-color: {tag.color}"
                onclick={() => handleTagSelect(tag)}
              >
                <span>{tag.name}</span>
              </button>
            {/each}
          </div>
          <div class="max-w-sm bg-white">
            <div class="mb-6">
              {#if !selectedTag}
                <p class="text-[16px] text-[#606166] mb-[20px] font-semibold">Filter by Color</p>
              {:else}
                <p class="text-[16px] text-[#606166] mb-[20px] font-semibold">Choose Color</p>
              {/if}
              <div class="grid grid-cols-5 gap-2">
                {#each colors as color}
                  <button 
                    class="size-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500 transition-transform transform hover:scale-110"
                    style="background-color: {color}"
                    class:ring-2={selectedColor === color}
                    onclick={() => selectedColor = color}
                  >
                    {#if selectedColor === color}
                      <span class="text-[#606166] text-shadow">✓</span>
                    {/if}
                  </button>
                {/each}
              </div>
            </div>
          <Dialog.Footer>
            {#if !selectedTag}
              <div class="flex flex-row items-center justify-center gap-8">
                <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleCancelEditTagFn}>Cancel</Button>
                <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={() => { openEditTagDialog = false; openChooseTagDialog = true; }}>Choose Tag</Button>
              </div>
            {:else}
              <div class="flex flex-row items-center justify-between gap-8">
                <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleEditDeleteFn} disabled={!selectedTag || isLoading}>
                  Delete
                </Button>
                <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleCancelEditTagFn}>Cancel</Button>
                <Button variant="ghost" class="flex gap-2 {isLoading ? 'text-[#313da3]' : 'text-[#FC570C]'} rounded justify-start text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleEditTagSave} disabled={!selectedTag || isLoading}>
                  {#if isLoading}
                    Save 
                    <Disc class="size-6 animate-spin" />
                  {:else}
                    Save
                  {/if}
                </Button>
              </div>
            {/if}
          </Dialog.Footer>
        </div>
        </Dialog.Content>
    </Dialog.Root>

    <Dialog.Root open={openDeleteAlertDialog} onOpenChange={(val: boolean) => { openDeleteAlertDialog = val}}>
        <Dialog.Content>
          <ChevronLeft class="size-5 top-4 cursor-pointer" onclick={() => {openDeleteAlertDialog = !openDeleteAlertDialog; openEditTagDialog = true}} />
          <Dialog.Header class="text-[20px] text-[#606166] font-semibold">
            <Dialog.Title>Delete Tag</Dialog.Title>
          </Dialog.Header>
           <div class="my-[15px]">
            <button
                class="inline-flex select-none items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#FC570C] rounded-[20px] text-[#423D3D] h-[29px]"
                style="background-color: {selectedTag.color}"
                disabled
              >
                <span>{selectedTag.name}</span>
              </button>
           </div>
          <Dialog.Description class="text-[16px] text-[#606166] font-semibold">
              Do you want to delete this tag for 'All' exercises, or for this 'Single' exercise?
          </Dialog.Description>
          <Dialog.Footer>
            <div class="flex flex-row items-center justify-between gap-8">
              <Button variant="ghost" class="text-[#FC570C] text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleDeleteAlertCancel} disabled={isLoading} >Cancel</Button>
              <Button variant="ghost" class="flex gap-2 {isLoading ? 'text-[#313da3]' : 'text-[#FC570C]'} rounded justify-start items-center text-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleDeleteAlertSingle} disabled={isLoading || !isExerciseInTag }>
                Single
              </Button>
              <Button variant="ghost" class="flex gap-2 {isLoading ? 'text-[#313da3]' : 'text-[#FC570C]'} rounded justify-start items-center font-boldtext-[16px] font-semibold focus:bg-transparent hover:bg-transparent active:bg-transparent" onclick={handleDeleteAlertAll} disabled={isLoading}>
                {#if isLoading}
                All 
                <Disc class="size-6 animate-spin" />
              {:else}
                All
              {/if}
              </Button>
            </div>
          </Dialog.Footer>
        </Dialog.Content>
        </Dialog.Root>

        <Dialog.Root open={dialogOpen} onOpenChange={(val) => (dialogOpen = val)}>
          <Dialog.Content>
              <Dialog.Header>
              <Dialog.Title class="mb-3">Add To Workout</Dialog.Title>
              <Dialog.Description >
                  <div class="flex flex-col gap-2 mb-3">
                    {#if $exerciseDetails.video_src}
                      <VideoPlayer 
                        poster={$exerciseDetails.Larg_Img_1 ?? ""}
                        src={$exerciseDetails.video_src} 
                        onclick={() => onVimeoOpen = true} 
                      />
                    {:else}
                      <button
                        onclick={() => onVimeoOpen = true}  
                      >
                        <img  
                        src={$exerciseDetails.Larg_Img_1 ?? ""} 
                        alt="video_not_available" class="w-full h-full" 
                      />
                      </button>
                    {/if}
                      <label class="text-center text-black text-[19px] m-3"  for="program">Select a Program</label>
      
                      <Select.Root bind:selected={selectedProgramFromStore!} onSelectedChange={(val: any) => {
                          selectedProgram.set(val)
                          //defaultProgram.set(val);
                      }}>
                          <Select.Trigger class="w-full">
                              <Select.Value class="text-[16px] text-black" color="#677489"  placeholder="Select a Program" />
                          </Select.Trigger>
                          <Select.Content>
                              {#each programs as program}
                                  <Select.Item value={program.id}>{program.name ? program.name : program.automated_name}</Select.Item>
                              {/each}
                          </Select.Content>
                      </Select.Root>
                  </div>
                  <div class="flex flex-col gap-2 mb-6">
      
                      <label class="text-center text-black text-[19px] m-3"  for="workout">Select a Workout</label>
                      <Select.Root selected={selectedWorkoutFromStore} onSelectedChange={(val: any) => selectedWorkout.set(val)}>
                      <Select.Trigger class="w-full">
                          <Select.Value class="text-[16px] my-5 text-black" color="#677489" placeholder="Select a Workout" />
                      </Select.Trigger>
                      <Select.Content>
                          {#each workouts as workout}
                              <Select.Item value={workout.id}>{workout.name ? workout.name : workout.automated_name}</Select.Item>
                          {/each}
                      </Select.Content>
                      </Select.Root>
                  </div>
                   <div class="flex justify-center">
                  <Button class="bg-red-500 m-auto text-white"  on:click={handleAddExercise}>{showMoveExerciseMenuItem ? "Move Exercise" : "Add"}</Button>
                </div>
              </Dialog.Description>
              </Dialog.Header>
          </Dialog.Content>
      </Dialog.Root>

     <AlertDialog.Root open={openDeleteExerciseDialog} closeOnEscape closeOnOutsideClick onOpenChange={(val) => { openDeleteExerciseDialog = val }}>
    <AlertDialog.Content>
      <AlertDialog.Header>
        <AlertDialog.Title>Confirm Deletion</AlertDialog.Title>
        <AlertDialog.Description>
            Once deleted, <b>{ selectedExercise?.name } cannot be recovered. Proceed?
            <!-- Once deleted, <b>{ selectedExercise?.name }</b> from workout <b>{name ? name : automated_name}</b> cannot be recovered. Proceed? -->
        </AlertDialog.Description>
      </AlertDialog.Header>
      <AlertDialog.Footer>
        <AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
        <AlertDialog.Action class="bg-red-500" on:click={() => handleDeleteExercise(get(current_workout_exercise_id))}>Delete</AlertDialog.Action>
      </AlertDialog.Footer>
    </AlertDialog.Content>
  </AlertDialog.Root>

{/if}


<Dialog.Root 
  open={onVimeoOpen}
  onOpenChange={(val: boolean) => { onVimeoOpen = val }}
>
 
  <Dialog.Content class="sm:w-[90%] md:max-w-[600px]  p-0">
    
    <div class="p-2">
      {#if $exerciseDetails.video_src}
        <VimeoPlayer vimeoUrl={$exerciseDetails.video_src ?? ""} />
      {:else}
        <img src={$exerciseDetails.Larg_Img_1 ?? ""} alt="video_not_available" class="w-full h-full" />
      {/if}
    </div>
   
  </Dialog.Content>
</Dialog.Root>

<style>
    .no-select-no-copy {
    user-select: none; /* Prevents text selection */
    pointer-events: none; /* Disables interactions */
    -webkit-touch-callout: none; /* Disables long-press menu on iOS */
}

.no-select-no-copy img, 
.no-select-no-copy video, 
.no-select-no-copy button {
    pointer-events: auto; /* I'm allowing interactions with images, videos, and buttons so video can be played */    
    -webkit-user-drag: none; /* Prevents dragging */
    user-select: none; /* Ensures no text selection */
}

/* Using this to create a div in the element to hold blurred content if necessary */
.no-select-no-copy::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 10;
}

/* I'm adding this to also prevent selection and copying of all children elements (very necessary) */
.no-select-no-copy * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; 
}
</style>
